use reforged_domain::error::RepositoryError;
use reforged_domain::models::profile::entity::Profile;
use reforged_domain::models::user::entity::User;
use reforged_domain::models::user::profile::UserProfile;
use reforged_domain::models::user::value_object::UserId;
use reforged_shared::IdTrait;
use sea_orm::{ColumnTrait, QueryOrder, QuerySelect};
use sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait};

use crate::mappers::profile_mapper::ProfileDbModelMapper;
use crate::{SeaORMErr, mappers::user_mapper::UserDbModelMapper};
use crate::mappers::user_color_mapper::UserColorDbModelMapper;
use crate::mappers::user_stat_mapper::UserStatDbModelMapper;
use crate::mappers::user_exp_mapper::UserExperienceDbModelMapper;
use crate::mappers::achievement_mapper::AchievementDbModelMapper;
use crate::mappers::guild_mapper::GuildDbModelMapper;
use crate::mappers::title_mapper::TitleDbModelMapper;
use crate::mappers::user_currency_mapper::UserCurrencyDbModelMapper;
use crate::mappers::user_item_mapper::UserItemDbModelMapper;
use crate::mappers::user_boost_mapper::UserBoostDbModelMapper;


use crate::models::users::Column as UsersColumn;
use crate::models::user_colors::Column as UserColorsColumn;
use crate::models::user_stats::Column as UserStatsColumn;
use crate::models::user_exps::Column as UserExpsColumn;
use crate::models::user_achievements::Column as UserAchievementsColumn;
use crate::models::user_guilds::Column as UserGuildsColumn;
use crate::models::user_titles::Column as UserTitlesColumn;
use crate::models::user_currencies::Column as UserCurrenciesColumn;
use crate::models::user_items::Column as UserItemsColumn;
use crate::models::user_boosts::Column as UserBoostsColumn;
use crate::models::user_friends::Column as UserFriendsColumn;
use crate::persistence::models::prelude::*;

use reforged_domain::repository::user_repository::UserReadRepository;

#[allow(dead_code)]
pub struct PostgresUserReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserReadRepository for PostgresUserReadRepository {
    async fn get_latest_id(&self) -> Result<UserId, RepositoryError> {
        let user = Users::find()
            .order_by_desc(UsersColumn::Id)
            .limit(1)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.map(|model| UserId::new(model.id))
            .ok_or(RepositoryError::ConnectionError)
    }

    async fn find_by_id(
        &self,
        id: &UserId,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find_by_id(id.get_id())
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!("with id {}", id)))
            .and_then(|(user, profile)| {
                let mapped_user = User::from(UserDbModelMapper::new(user));
                let mapped_profile =
                    profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

                Ok(Some((mapped_user, mapped_profile)))
            })
    }

    async fn find_by_username(
        &self,
        username: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find()
            .filter(UsersColumn::Username.eq(username))
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!(
            "with username {}",
            username
        )))
        .and_then(|(user, profile)| {
            let mapped_user = User::from(UserDbModelMapper::new(user));
            let mapped_profile =
                profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

            Ok(Some((mapped_user, mapped_profile)))
        })
    }

    async fn find_by_email(
        &self,
        email: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find()
            .filter(UsersColumn::Email.eq(email))
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!("with email {}", email)))
            .and_then(|(user, profile)| {
                let mapped_user = User::from(UserDbModelMapper::new(user));
                let mapped_profile =
                    profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

                Ok(Some((mapped_user, mapped_profile)))
            })
    }

    async fn get_with_profile(&self, id: &UserId) -> Result<Option<UserProfile>, RepositoryError> {
        // Get user and profile
        let user_with_profile = Users::find_by_id(id.get_id())
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let (user_model, profile_model) = match user_with_profile {
            Some((user, Some(profile))) => (user, profile),
            Some((_, None)) => return Ok(None), // User exists but no profile
            None => return Ok(None), // User doesn't exist
        };

        // Convert user and profile
        let user = User::from(UserDbModelMapper::new(user_model));
        let profile = Profile::from(ProfileDbModelMapper::new(profile_model));

        // Get user color (required)
        let user_color_model = UserColors::find()
            .filter(UserColorsColumn::UserId.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?
            .ok_or(RepositoryError::NotFound(format!("UserColor for user {}", id)))?;
        let user_color = UserColorDbModelMapper::new(user_color_model).into();

        // Get user stats (required)
        let user_stat_model = UserStats::find()
            .filter(UserStatsColumn::UserId.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?
            .ok_or(RepositoryError::NotFound(format!("UserStat for user {}", id)))?;
        let user_stats = UserStatDbModelMapper::new(user_stat_model).into();

        // Get user experience (required)
        let user_exp_model = UserExps::find()
            .filter(UserExpsColumn::UserId.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?
            .ok_or(RepositoryError::NotFound(format!("UserExperience for user {}", id)))?;
        let user_exp = UserExperienceDbModelMapper::new(user_exp_model).into();

        // Get user currencies (required)
        let user_currency_model = UserCurrencies::find()
            .filter(UserCurrenciesColumn::UserId.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?
            .ok_or(RepositoryError::NotFound(format!("UserCurrency for user {}", id)))?;
        let user_currencies = UserCurrencyDbModelMapper::new(user_currency_model).into();

        // Get user boosts (required)
        let user_boost_model = UserBoosts::find()
            .filter(UserBoostsColumn::UserId.eq(id.get_id()))
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?
            .ok_or(RepositoryError::NotFound(format!("UserBoost for user {}", id)))?;
        let user_boosts = UserBoostDbModelMapper::new(user_boost_model).into();

        // Get user achievements
        let achievement_models = UserAchievements::find()
            .filter(UserAchievementsColumn::UserId.eq(id.get_id()))
            .find_also_related(Achievements)
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let achievements = achievement_models
            .into_iter()
            .filter_map(|(_, achievement)| achievement)
            .map(|model| AchievementDbModelMapper::new(model).into())
            .collect();

        // Get user friends - we need to do this with a separate query since the relationship is complex
        let user_friend_models = UserFriends::find()
            .filter(UserFriendsColumn::UserId.eq(id.get_id()))
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let mut friends = Vec::new();
        for user_friend in user_friend_models {
            if let Some(friend_user) = Users::find_by_id(user_friend.friend_id)
                .one(&self.pool)
                .await
                .map_err(|e| SeaORMErr::from(e))?
            {
                friends.push(User::from(UserDbModelMapper::new(friend_user)));
            }
        }

        // Get user equipment (items)
        let equipment_models = UserItems::find()
            .filter(UserItemsColumn::UserId.eq(id.get_id()))
            .all(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let equipment = equipment_models
            .into_iter()
            .map(|model| UserItemDbModelMapper::new(model).into())
            .collect();

        // Get user title (optional) - get the most recent one
        let user_title_with_title = UserTitles::find()
            .filter(UserTitlesColumn::UserId.eq(id.get_id()))
            .find_also_related(Titles)
            .order_by_desc(UserTitlesColumn::Date)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let title = user_title_with_title
            .and_then(|(_, title)| title)
            .map(|model| TitleDbModelMapper::new(model).into());

        // Get user guild (optional)
        let user_guild_with_guild = UserGuilds::find()
            .filter(UserGuildsColumn::UserId.eq(id.get_id()))
            .find_also_related(Guilds)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        let guild = user_guild_with_guild
            .and_then(|(_, guild)| guild)
            .map(|model| GuildDbModelMapper::new(model).into());

        // Build UserProfile
        let user_profile = UserProfile::builder()
            .user(user)
            .info(profile)
            .color(user_color)
            .maybe_title(title)
            .stats(user_stats)
            .exp(user_exp)
            .achievements(achievements)
            .friends(friends)
            .maybe_guild(guild)
            .currencies(user_currencies)
            .equipment(equipment)
            .boosts(user_boosts)
            .build();

        Ok(Some(user_profile))
    }
}
