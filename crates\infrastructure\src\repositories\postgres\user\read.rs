use reforged_domain::error::RepositoryError;
use reforged_domain::models::profile::entity::Profile;
use reforged_domain::models::user::entity::User;
use reforged_domain::models::user::profile::UserProfile;
use reforged_domain::models::user::value_object::UserId;
use reforged_shared::IdTrait;
use sea_orm::QueryOrder;
use sea_orm::{ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, QueryOrder, prelude::async_trait::async_trait};

use crate::mappers::profile_mapper::ProfileDbModelMapper;
use crate::mappers::user_mapper::UserDbModelMapper;
use crate::mappers::guild_mapper::GuildDbModelMapper;
use crate::mappers::title_mapper::TitleDbModelMapper;
use crate::mappers::user_boost_mapper::UserBoostDbModelMapper;
use crate::mappers::user_color_mapper::UserColorDbModelMapper;
use crate::mappers::user_currency_mapper::UserCurrencyDbModelMapper;
use crate::mappers::user_exp_mapper::UserExperienceDbModelMapper;
use crate::mappers::user_item_mapper::UserItemDbModelMapper;
use crate::mappers::user_stat_mapper::UserStatDbModelMapper;
use crate::SeaORMErr;

use crate::models::user_boosts::Column as UserBoostsColumn;
use crate::models::user_colors::Column as UserColorsColumn;
use crate::models::user_currencies::Column as UserCurrenciesColumn;
use crate::models::user_exps::Column as UserExpsColumn;
use crate::models::user_guilds::Column as UserGuildsColumn;
use crate::models::user_items::Column as UserItemsColumn;
use crate::models::user_stats::Column as UserStatsColumn;
use crate::models::user_titles::Column as UserTitlesColumn;
use crate::models::users::Column as UsersColumn;
use crate::persistence::models::prelude::*;

use reforged_domain::repository::user_repository::UserReadRepository;

#[allow(dead_code)]
pub struct PostgresUserReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserReadRepository for PostgresUserReadRepository {
    async fn get_latest_id(&self) -> Result<UserId, RepositoryError> {
        let user = Users::find()
            .order_by_desc(UsersColumn::Id)
            .limit(1)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.map(|model| UserId::new(model.id))
            .ok_or(RepositoryError::ConnectionError)
    }

    async fn find_by_id(
        &self,
        id: &UserId,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find_by_id(id.get_id())
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!("with id {}", id)))
            .and_then(|(user, profile)| {
                let mapped_user = User::from(UserDbModelMapper::new(user));
                let mapped_profile =
                    profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

                Ok(Some((mapped_user, mapped_profile)))
            })
    }

    async fn find_by_username(
        &self,
        username: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find()
            .filter(UsersColumn::Username.eq(username))
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!(
            "with username {}",
            username
        )))
        .and_then(|(user, profile)| {
            let mapped_user = User::from(UserDbModelMapper::new(user));
            let mapped_profile =
                profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

            Ok(Some((mapped_user, mapped_profile)))
        })
    }

    async fn find_by_email(
        &self,
        email: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find()
            .filter(UsersColumn::Email.eq(email))
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!("with email {}", email)))
            .and_then(|(user, profile)| {
                let mapped_user = User::from(UserDbModelMapper::new(user));
                let mapped_profile =
                    profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

                Ok(Some((mapped_user, mapped_profile)))
            })
    }

    async fn get_with_profile(&self, id: &UserId) -> Result<Option<UserProfile>, RepositoryError> {
        // Execute all queries concurrently for better performance
        let user_id = id.get_id();

        // Query 1: Get user and profile with JOIN
        let user_profile_future = Users::find_by_id(user_id)
            .find_also_related(Profiles)
            .one(&self.pool);

        // Query 2: Get user color (required)
        let user_color_future = UserColors::find()
            .filter(UserColorsColumn::UserId.eq(user_id))
            .one(&self.pool);

        // Query 3: Get user stats (required)
        let user_stats_future = UserStats::find()
            .filter(UserStatsColumn::UserId.eq(user_id))
            .one(&self.pool);

        // Query 4: Get user experience (required)
        let user_exp_future = UserExps::find()
            .filter(UserExpsColumn::UserId.eq(user_id))
            .one(&self.pool);

        // Query 5: Get user currencies (required)
        let user_currencies_future = UserCurrencies::find()
            .filter(UserCurrenciesColumn::UserId.eq(user_id))
            .one(&self.pool);

        // Query 6: Get user boosts (required)
        let user_boosts_future = UserBoosts::find()
            .filter(UserBoostsColumn::UserId.eq(user_id))
            .one(&self.pool);

        // Query 7: Get user equipment (items)
        let equipment_future = UserItems::find()
            .filter(UserItemsColumn::UserId.eq(user_id))
            .all(&self.pool);

        // Query 8: Get user titles with JOIN - get all titles
        let titles_future = UserTitles::find()
            .filter(UserTitlesColumn::UserId.eq(user_id))
            .find_also_related(Titles)
            .all(&self.pool);

        // Query 9: Get user guild (optional) with JOIN
        let guild_future = UserGuilds::find()
            .filter(UserGuildsColumn::UserId.eq(user_id))
            .find_also_related(Guilds)
            .one(&self.pool);

        // Execute all queries concurrently
        let (
            user_profile_result,
            user_color_result,
            user_stats_result,
            user_exp_result,
            user_currencies_result,
            user_boosts_result,
            equipment_result,
            titles_result,
            guild_result,
        ) = tokio::try_join!(
            user_profile_future,
            user_color_future,
            user_stats_future,
            user_exp_future,
            user_currencies_future,
            user_boosts_future,
            equipment_future,
            titles_future,
            guild_future,
        )
        .map_err(SeaORMErr::from)?;

        // Check if user exists
        let (user_model, profile_model) = user_profile_result
            .ok_or(RepositoryError::NotFound(format!("User with id {}", id)))?;

        // Convert to domain entities
        let user = User::from(UserDbModelMapper::new(user_model));
        let profile = Profile::from(ProfileDbModelMapper::new(profile_model.ok_or(
            RepositoryError::NotFound(format!("Profile for user {}", id))
        )?));

        // Process required entities with error handling
        let user_color = user_color_result
            .ok_or(RepositoryError::NotFound(format!(
                "UserColor for user {}",
                id
            )))?;
        let user_color = UserColorDbModelMapper::new(user_color).into();

        let user_stats = user_stats_result
            .ok_or(RepositoryError::NotFound(format!(
                "UserStat for user {}",
                id
            )))?;
        let user_stats = UserStatDbModelMapper::new(user_stats).into();

        let user_exp = user_exp_result
            .ok_or(RepositoryError::NotFound(format!(
                "UserExperience for user {}",
                id
            )))?;
        let user_exp = UserExperienceDbModelMapper::new(user_exp).into();

        let user_currencies = user_currencies_result
            .ok_or(RepositoryError::NotFound(format!(
                "UserCurrency for user {}",
                id
            )))?;
        let user_currencies = UserCurrencyDbModelMapper::new(user_currencies).into();

        let user_boosts = user_boosts_result
            .ok_or(RepositoryError::NotFound(format!(
                "UserBoost for user {}",
                id
            )))?;
        let user_boosts = UserBoostDbModelMapper::new(user_boosts).into();

        // Process equipment
        let equipment = equipment_result
            .into_iter()
            .map(|model| UserItemDbModelMapper::new(model).into())
            .collect();

        // Process titles
        let titles = titles_result
            .into_iter()
            .filter_map(|(_, title_model)| title_model)
            .map(|title_model| TitleDbModelMapper::new(title_model).into())
            .collect();

        // Process guild (optional)
        let guild = guild_result
            .and_then(|(_, guild_model)| guild_model)
            .map(|guild_model| GuildDbModelMapper::new(guild_model).into());

        // Build and return UserProfile
        let user_profile = UserProfile::builder()
            .user(user)
            .info(profile)
            .color(user_color)
            .stats(user_stats)
            .exp(user_exp)
            .currencies(user_currencies)
            .boosts(user_boosts)
            .equipment(equipment)
            .achievements(vec![]) // TODO: Add achievements query
            .maybe_title(title)
            .maybe_guild(guild)
            .friends(vec![]) // TODO: Add friends query
            .factions(vec![]) // TODO: Add factions query
            .build();

        Ok(Some(user_profile))
    }
}
