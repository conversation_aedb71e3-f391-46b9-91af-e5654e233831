use reforged_domain::error::RepositoryError;
use reforged_domain::models::profile::entity::Profile;
use reforged_domain::models::user::entity::User;
use reforged_domain::models::user::profile::UserProfile;
use reforged_domain::models::user::value_object::UserId;
use reforged_shared::IdTrait;
use sea_orm::{ColumnTrait, QueryOrder, QuerySelect};
use sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, prelude::async_trait::async_trait};

use crate::mappers::profile_mapper::ProfileDbModelMapper;
use crate::{SeaORMErr, mappers::user_mapper::UserDbModelMapper};

use crate::models::users::Column as UsersColumn;
use crate::persistence::models::prelude::*;

use reforged_domain::repository::user_repository::UserReadRepository;

#[allow(dead_code)]
pub struct PostgresUserReadRepository {
    pool: DatabaseConnection,
}

impl PostgresUserReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserReadRepository for PostgresUserReadRepository {
    async fn get_latest_id(&self) -> Result<UserId, RepositoryError> {
        let user = Users::find()
            .order_by_desc(UsersColumn::Id)
            .limit(1)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.map(|model| UserId::new(model.id))
            .ok_or(RepositoryError::ConnectionError)
    }

    async fn find_by_id(
        &self,
        id: &UserId,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find_by_id(id.get_id())
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!("with id {}", id)))
            .and_then(|(user, profile)| {
                let mapped_user = User::from(UserDbModelMapper::new(user));
                let mapped_profile =
                    profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

                Ok(Some((mapped_user, mapped_profile)))
            })
    }

    async fn find_by_username(
        &self,
        username: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find()
            .filter(UsersColumn::Username.eq(username))
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!(
            "with username {}",
            username
        )))
        .and_then(|(user, profile)| {
            let mapped_user = User::from(UserDbModelMapper::new(user));
            let mapped_profile =
                profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

            Ok(Some((mapped_user, mapped_profile)))
        })
    }

    async fn find_by_email(
        &self,
        email: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError> {
        let user = Users::find()
            .filter(UsersColumn::Email.eq(email))
            .find_also_related(Profiles)
            .one(&self.pool)
            .await
            .map_err(|e| SeaORMErr::from(e))?;

        user.ok_or(RepositoryError::NotFound(format!("with email {}", email)))
            .and_then(|(user, profile)| {
                let mapped_user = User::from(UserDbModelMapper::new(user));
                let mapped_profile =
                    profile.map(|profile| Profile::from(ProfileDbModelMapper::new(profile)));

                Ok(Some((mapped_user, mapped_profile)))
            })
    }

    async fn get_with_profile(&self, id: &UserId) -> Result<Option<UserProfile>, RepositoryError> {
        todo!()
    }
}
