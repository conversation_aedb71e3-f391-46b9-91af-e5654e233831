//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.0

use chrono::NaiveDateTime;
use sea_orm::{FromQueryResult, entity::prelude::*};
use serde::Deserialize;

#[derive(<PERSON><PERSON>, Debug, <PERSON>ialEq, DeriveEntityModel, Eq, Deserialize)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    #[sea_orm(unique)]
    pub username: String,
    pub hash: String,
    pub salt: String,
    pub role_id: Uuid,
    pub title_id: Uuid,
    #[sea_orm(unique)]
    pub email: String,
    pub date_created: DateTime,
    pub last_login: DateTime,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::articles::Entity")]
    Articles,
    #[sea_orm(has_many = "super::deleted_user_items::Entity")]
    DeletedUserItems,
    #[sea_orm(has_many = "super::guild_items::Entity")]
    GuildItems,
    #[sea_orm(has_many = "super::guilds::Entity")]
    Guilds,
    #[sea_orm(has_many = "super::password_reset_tokens::Entity")]
    PasswordResetTokens,
    #[sea_orm(has_many = "super::profiles::Entity")]
    Profiles,
    #[sea_orm(
        belongs_to = "super::roles::Entity",
        from = "Column::RoleId",
        to = "super::roles::Column::Id",
        on_update = "Cascade",
        on_delete = "Restrict"
    )]
    Roles,
    #[sea_orm(has_many = "super::sessions::Entity")]
    Sessions,
    #[sea_orm(
        belongs_to = "super::titles::Entity",
        from = "Column::TitleId",
        to = "super::titles::Column::Id",
        on_update = "Cascade",
        on_delete = "Cascade"
    )]
    Titles,
    #[sea_orm(has_many = "super::user_achievements::Entity")]
    UserAchievements,
    #[sea_orm(has_many = "super::user_boosts::Entity")]
    UserBoosts,
    #[sea_orm(has_many = "super::user_browsers::Entity")]
    UserBrowsers,
    #[sea_orm(has_many = "super::user_colors::Entity")]
    UserColors,
    #[sea_orm(has_many = "super::user_currencies::Entity")]
    UserCurrencies,
    #[sea_orm(has_many = "super::user_exps::Entity")]
    UserExps,
    #[sea_orm(has_many = "super::user_factions::Entity")]
    UserFactions,
    #[sea_orm(has_many = "super::user_guilds::Entity")]
    UserGuilds,
    #[sea_orm(has_many = "super::user_items::Entity")]
    UserItems,
    #[sea_orm(has_many = "super::user_livedrops::Entity")]
    UserLivedrops,
    #[sea_orm(has_many = "super::user_logins::Entity")]
    UserLogins,
    #[sea_orm(has_many = "super::user_purchases::Entity")]
    UserPurchases,
    #[sea_orm(has_many = "super::user_quests::Entity")]
    UserQuests,
    #[sea_orm(has_many = "super::user_redeems::Entity")]
    UserRedeems,
    #[sea_orm(has_many = "super::user_reports::Entity")]
    UserReports,
    #[sea_orm(has_many = "super::user_slots::Entity")]
    UserSlots,
    #[sea_orm(has_many = "super::user_stats::Entity")]
    UserStats,
    #[sea_orm(has_many = "super::user_titles::Entity")]
    UserTitles,
    #[sea_orm(has_many = "super::user_friends::Entity")]
    UserFriendsAsUser, // Friends where this user is the initiator
    #[sea_orm(has_many = "super::user_friends::Entity")]
    UserFriendsAsFriend, // Friends where this user is the initiator
}

impl Related<super::articles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Articles.def()
    }
}

impl Related<super::deleted_user_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::DeletedUserItems.def()
    }
}

impl Related<super::guild_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::GuildItems.def()
    }
}

impl Related<super::guilds::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserGuilds.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::user_guilds::Relation::Users.def().rev())
    }
}

impl Related<super::password_reset_tokens::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::PasswordResetTokens.def()
    }
}

impl Related<super::profiles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Profiles.def()
    }
}

impl Related<super::roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Roles.def()
    }
}

impl Related<super::sessions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Sessions.def()
    }
}

impl Related<super::titles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserTitles.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::user_titles::Relation::Users.def().rev())
    }
}

impl Related<super::user_achievements::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserAchievements.def()
    }
}

impl Related<super::user_boosts::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserBoosts.def()
    }
}

impl Related<super::user_browsers::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserBrowsers.def()
    }
}

impl Related<super::user_colors::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserColors.def()
    }
}

impl Related<super::user_currencies::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserCurrencies.def()
    }
}

impl Related<super::user_exps::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserExps.def()
    }
}

impl Related<super::user_factions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserFactions.def()
    }
}

impl Related<super::user_guilds::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserGuilds.def()
    }
}

impl Related<super::user_items::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserItems.def()
    }
}

impl Related<super::user_livedrops::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserLivedrops.def()
    }
}

impl Related<super::user_logins::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserLogins.def()
    }
}

impl Related<super::user_purchases::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserPurchases.def()
    }
}

impl Related<super::user_quests::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserQuests.def()
    }
}

impl Related<super::user_redeems::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserRedeems.def()
    }
}

impl Related<super::user_reports::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserReports.def()
    }
}

impl Related<super::user_slots::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserSlots.def()
    }
}

impl Related<super::user_stats::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserStats.def()
    }
}

impl Related<super::user_titles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserTitles.def()
    }
}

impl Related<super::items::Entity> for Entity {
    fn to() -> RelationDef {
        super::user_items::Relation::Items.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::user_items::Relation::Users.def().rev())
    }
}

impl Related<super::achievements::Entity> for Entity {
    fn to() -> RelationDef {
        super::user_achievements::Relation::Achievements.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::user_achievements::Relation::Users.def().rev())
    }
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        super::user_friends::Relation::Friend.def()
    }

    fn via() -> Option<RelationDef> {
        Some(super::user_friends::Relation::User.def().rev())
    }
}

impl Related<super::user_friends::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserFriendsAsUser.def()
    }
}

impl Related<super::factions::Entity> for Entity {
    fn to() -> RelationDef {
        super::user_factions::Relation::Factions.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::user_factions::Relation::Users.def().rev())
    }
}

impl ActiveModelBehavior for ActiveModel {}
