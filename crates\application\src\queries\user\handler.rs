use reforged_shared::{IdTrait, Value, uuid_generator::uuid_to_u64};
use serde::Serialize;
use std::sync::Arc;
use tracing::info;

use async_trait::async_trait;
use reforged_domain::{
    models::{
        profile::entity::Profile,
        user::{
            entity::User,
            value_object::{Email, HashedPassword, UserId, UserRole, Username},
        },
    },
    repository::user_repository::UserReadRepository,
};

use crate::traits::QueryHandler;

use super::queries::{
    GetLatestIdQuery, GetUserByEmailQuery, GetUserByIdQuery, GetUserByUsernameQuery, ListUsersQuery,
};

#[derive(Clone, bon::Builder)]
pub struct UserQueryHandler {
    user_repo: Arc<dyn UserReadRepository>,
}

#[async_trait]
impl QueryHandler<GetUserByEmailQuery> for UserQueryHandler {
    type Output = Option<UserResponse>;

    async fn handle(
        &self,
        query: GetUserByEmailQuery,
    ) -> Result<Option<UserResponse>, crate::error::ApplicationError> {
        let user_repo = self.user_repo.clone();
        let user = user_repo
            .find_by_email(&query.email)
            .await?
            .map(UserResponse::from);

        Ok(user)
    }
}

#[async_trait]
impl QueryHandler<GetUserByIdQuery> for UserQueryHandler {
    type Output = Option<UserResponse>;

    async fn handle(
        &self,
        query: GetUserByIdQuery,
    ) -> Result<Option<UserResponse>, crate::error::ApplicationError> {
        let id = UserId::new(query.id());
        let user_repo = self.user_repo.clone();
        let user = user_repo.find_by_id(&id).await?.map(UserResponse::from);

        let user_profile = user_repo.get_with_profile(&id).await?;

        Ok(user)
    }
}

#[async_trait]
impl QueryHandler<ListUsersQuery> for UserQueryHandler {
    type Output = ();

    async fn handle(&self, _query: ListUsersQuery) -> Result<(), crate::error::ApplicationError> {
        Ok(())
    }
}

#[async_trait]
impl QueryHandler<GetUserByUsernameQuery> for UserQueryHandler {
    type Output = Option<UserResponse>;

    async fn handle(
        &self,
        query: GetUserByUsernameQuery,
    ) -> Result<Option<UserResponse>, crate::error::ApplicationError> {
        let user_repo = self.user_repo.clone();
        let user = user_repo
            .find_by_username(&query.username)
            .await?
            .map(UserResponse::from);

        Ok(user)
    }
}

#[async_trait]
impl QueryHandler<GetLatestIdQuery> for UserQueryHandler {
    type Output = UserId;

    async fn handle(
        &self,
        _query: GetLatestIdQuery,
    ) -> Result<UserId, crate::error::ApplicationError> {
        let user_repo = self.user_repo.clone();
        let user_id = user_repo.get_latest_id().await.unwrap_or_default();

        Ok(user_id)
    }
}

#[derive(Serialize, bon::Builder)]
pub struct UserResponse {
    pub id: uuid::Uuid,
    pub pid: u64,
    pub username: String,
    #[serde(skip)]
    pub email: String,
    pub gender: String,
    pub role: String,
    #[serde(skip)]
    pub hashed_password: String,
    #[serde(skip)]
    pub salt: String,
}

impl From<(User, Option<Profile>)> for UserResponse {
    fn from(value: (User, Option<Profile>)) -> Self {
        let (user, profile) = value;
        let gender = profile
            .map(|profile| profile.gender().to_string())
            .unwrap_or_default();
        let pid = uuid_to_u64(&user.id().get_id());

        Self::builder()
            .id(user.id().get_id())
            .pid(pid)
            .username(user.username().value())
            .email(user.email().value())
            .gender(gender)
            .role(user.role().to_string()) // TODO: wrong mapping of role
            .hashed_password(user.hashed_password().hash().to_string())
            .salt(user.hashed_password().salt().to_string())
            .build()
    }
}

impl From<UserResponse> for User {
    fn from(value: UserResponse) -> Self {
        let UserResponse {
            id,
            pid: _,
            username,
            email,
            gender,
            role,
            hashed_password,
            salt,
        } = value;

        _ = gender;
        let role = UserRole::try_from(role).unwrap_or_default();

        User::builder()
            .id(UserId::new(id))
            .username(Username::new(username))
            .email(Email::new(email))
            .role(role)
            .hashed_password(HashedPassword::new(hashed_password, salt))
            .build()
    }
}
