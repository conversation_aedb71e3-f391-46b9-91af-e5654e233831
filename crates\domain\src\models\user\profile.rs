use getset::{<PERSON><PERSON>, <PERSON><PERSON>};

use crate::models::{
    achievement::entity::Achievement, faction::entity::Faction, guild::entity::Guild,
    profile::entity::Profile, title::entity::Title, user::entity::User,
    user_boost::entity::UserBoost, user_color::entity::UserColor,
    user_currency::entity::UserCurrency, user_exp::entity::UserExperience,
    user_item::entity::UserItem, user_stat::entity::UserStat,
};

#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult, bon::<PERSON><PERSON><PERSON>, Get<PERSON>, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserProfile {
    /// Information
    user: User,
    info: Profile,

    /// Appearance
    color: UserColor,

    /// User progression
    title: Option<Title>,
    stats: UserStat,
    exp: UserExperience,

    /// User social
    achievements: Vec<Achievement>,
    friends: Vec<User>,
    guild: Option<Guild>,
    factions: Vec<Faction>,

    /// Economy
    currencies: UserCurrency,
    equipment: Vec<UserItem>,
    boosts: UserBoost,
}
