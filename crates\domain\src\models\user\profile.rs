use getset::{<PERSON><PERSON>, Set<PERSON>};

use crate::models::{
    achievement::entity::Achievement, guild::entity::Guild, profile::entity::Profile,
    title::entity::Title, user::entity::User, user_boost::entity::UserBoost,
    user_color::entity::UserColor, user_currency::entity::UserCurrency,
    user_exp::entity::UserExperience, user_item::entity::UserItem, user_stat::entity::UserStat,
};

#[derive(Debug, <PERSON><PERSON>, Default, bon::Builder, Getters, Setters)]
#[getset(get = "pub", set = "pub")]
pub struct UserProfile {
    /// Information
    user: User,
    info: Profile,

    /// Appearance
    color: UserColor,

    /// User progression
    title: Option<Title>,
    stats: UserStat,
    exp: UserExperience,

    /// User social
    achievements: Vec<Achievement>,
    friends: Vec<User>,
    guild: Option<Guild>,

    /// Economy
    currencies: UserCurrency,
    equipment: Vec<UserItem>,
    boosts: UserBoost,
}
